<template>
  <div class="audio-recorder">
    <!-- 标题栏 - 使用系统原生控制按钮 -->
    <div class="title-bar">
      <h1 class="title">录音器</h1>
      <div>
        <button class="minimize-btn" @click="minimizeToDesktop" title="最小化">
          <svg viewBox="0 0 24 24" class="icon">
            <path d="M19 13H5v-2h14v2z" />
          </svg>
        </button>
        <!-- 关闭按钮 -->
        <!-- <button
          class="close-btn"
          :class="{ disabled: saveLoading }"
          @click="closeWindow"
          @contextmenu="showCloseMenu"
          title="关闭应用"
        >
          <svg viewBox="0 0 24 24" class="icon">
            <path
              d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
            />
          </svg>
        </button> -->
      </div>
    </div>
    <!-- 录音状态指示器 -->
    <div class="recorder-container">
      <!-- 主录音按钮 -->
      <button
        class="record-button"
        :class="{
          recording: isRecording,
          disabled: recordDisabled,
        }"
        @click="toggleRecording"
        :disabled="recordDisabled"
      >
        <!-- 录音图标 -->
        <div class="record-icon" v-if="!isRecording">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path
              d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"
            />
            <path
              d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"
            />
          </svg>
        </div>

        <!-- 停止图标 -->
        <div class="stop-icon" v-else>
          <svg viewBox="0 0 24 24" fill="currentColor">
            <rect x="6" y="6" width="12" height="12" rx="2" />
          </svg>
        </div>

        <!-- 脉冲动画 -->
        <div class="pulse" v-if="isRecording"></div>
      </button>

      <!-- 录音时长显示 -->
      <div class="duration" v-if="isRecording">
        {{ formatDuration(recordingDuration) }}
      </div>
    </div>

    <!-- 状态文本 -->
    <div class="status-text">
      <span v-if="saveLoading">音频上传中...</span>
      <template v-else>
        <span v-if="isStopped">开始录音</span>
        <span v-else-if="isLoading">录音准备中...</span>
        <span v-else-if="isSettling">文件生成中...</span>
        <span v-else-if="isError">打开录音失败</span>
        <span v-else-if="isRecording">停止录音</span>
      </template>
    </div>

    <!-- 错误提示 -->
    <!-- <div class="error-message" v-if="errorMessage">
      {{ errorMessage }}
    </div> -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onUnmounted, watchEffect } from 'vue'
import { useRecorder } from '@/hooks/useRecorder'
import { RecordingState } from '@/constants/app.constants'
import { watch } from 'fs'

const props = defineProps<{
  saveLoading: boolean
  meetingId: number | null
}>()
// 定义组件事件
const emit = defineEmits<{
  recordingComplete: [blob: Blob, duration: number]
  recordingStart: []
  recordingError: [error: string]
}>()

// 响应式数据
const errorMessage = ref<string>('')
const recordingDuration = ref<number>(0)
const durationTimer = ref<number | null>(null)

// 初始化录音器
const { audioState, recOpen, recStop } = useRecorder()

// 计算属性
const isRecording = computed(() => audioState.value === RecordingState.RECORDING)
const isLoading = computed(() => audioState.value === RecordingState.LOAD)
const isSettling = computed(() => audioState.value === RecordingState.SETTLE)
const isError = computed(() => audioState.value === RecordingState.ERROR)
const isStopped = computed(() => audioState.value === RecordingState.STOPPED)

watchEffect(() => {
  console.log(audioState.value, '------')
})
const recordDisabled = computed(() => {
  return props.saveLoading || !props.meetingId || isLoading.value || isSettling.value
})
/**
 * 格式化录音时长显示
 * @param seconds 秒数
 * @returns 格式化的时间字符串 (mm:ss)
 */
const formatDuration = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
}

/**
 * 开始录音时长计时
 */
const startDurationTimer = (): void => {
  recordingDuration.value = 0
  durationTimer.value = window.setInterval(() => {
    recordingDuration.value++
  }, 1000)
}

/**
 * 停止录音时长计时
 */
const stopDurationTimer = (): void => {
  if (durationTimer.value) {
    clearInterval(durationTimer.value)
    durationTimer.value = null
  }
}

/**
 * 开始录音
 */
const startRecording = async (): Promise<void> => {
  try {
    errorMessage.value = ''
    await recOpen()
    startDurationTimer()
    emit('recordingStart')
    console.log('录音开始')
  } catch (error) {
    const errorMsg = error instanceof Error ? error.message : '录音启动失败'
    errorMessage.value = errorMsg
    emit('recordingError', errorMsg)
    console.error('录音启动失败:', error)
  }
}

/**
 * 停止录音
 */
const stopRecording = (): void => {
  stopDurationTimer()
  recStop((blob: Blob, duration: number) => {
    emit('recordingComplete', blob, duration)
  })
}

/**
 * 切换录音状态
 */
const toggleRecording = (): void => {
  if (recordDisabled.value) return
  if (isRecording.value) {
    audioState.value = RecordingState.SETTLE
    console.log(1, audioState.value)

    stopRecording()
  } else {
    startRecording()
  }
}

/**
 * 最小化到桌面组件
 */
const minimizeToDesktop = (): void => {
  // 如果是 Electron 环境，调用 Electron API
  if (window.electronAPI) {
    window.electronAPI.minimizeWindow()
  } else {
    console.log('最小化功能需要在 Electron 环境中使用')
  }
}

/**
 * 关闭窗口 (隐藏到托盘)
 */
const closeWindow = (): void => {
  // 如果是 Electron 环境，调用 Electron API
  if (props.saveLoading) return
  if (window.electronAPI) {
    window.electronAPI.closeWindow()
  } else {
    console.log('关闭功能需要在 Electron 环境中使用')
  }
}

/**
 * 显示关闭菜单 (右键点击关闭按钮时)
 */
const showCloseMenu = (event: MouseEvent): void => {
  event.preventDefault()
  // 右键点击时直接退出应用
  if (window.electronAPI) {
    window.electronAPI.quitApp()
  } else {
    console.log('退出功能需要在 Electron 环境中使用')
  }
}

// 组件卸载时清理定时器
onUnmounted(() => {
  stopDurationTimer()
})
</script>

<style scoped>
.audio-recorder {
  display: flex;
  flex-direction: column;
  width: 200px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  min-height: 150px;
  position: relative;
}

.title-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  margin-bottom: 30px;
  background: rgba(0, 0, 0, 0.1);
  -webkit-app-region: drag;
  user-select: none;
  border-radius: 20px 20px 0 0;
}

.title-bar > div {
  display: flex;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  color: #fff;
}

.minimize-btn,
.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.3);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  -webkit-app-region: no-drag;
}

.close-btn {
  margin-left: 10px;
}
.close-btn.disabled {
  opacity: 0.3;
  cursor: not-allowed;
}
.minimize-btn:hover,
.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.minimize-btn .icon,
.close-btn .icon {
  width: 16px;
  height: 16px;
  fill: currentColor;
}

.recorder-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 25px;
}

@keyframes spectrum-pulse {
  0%,
  100% {
    height: 20px;
    background: rgba(255, 255, 255, 0.3);
  }
  25% {
    height: 35px;
    background: rgba(255, 107, 107, 0.7);
  }
  50% {
    height: 50px;
    background: rgba(255, 71, 87, 1);
    box-shadow: 0 0 10px rgba(255, 71, 87, 0.5);
  }
  75% {
    height: 30px;
    background: rgba(255, 107, 107, 0.7);
  }
}

/* 主录音按钮 */
.record-button {
  position: relative;
  width: 80px;
  height: 80px;
  border: none;
  border-radius: 50%;
  background: linear-gradient(145deg, #ff6b6b, #ee5a52);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(238, 90, 82, 0.4);
  z-index: 10;
}

.record-button:hover:not(.disabled) {
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(238, 90, 82, 0.6);
}

.record-button.recording {
  background: linear-gradient(145deg, #ff4757, #ff3742);
  animation: recording-pulse 1.5s infinite;
}

.record-button.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

@keyframes recording-pulse {
  0%,
  100% {
    box-shadow: 0 4px 15px rgba(255, 71, 87, 0.4);
  }
  50% {
    box-shadow: 0 4px 25px rgba(255, 71, 87, 0.8);
  }
}

/* 图标样式 */
.record-icon svg,
.stop-icon svg {
  width: 32px;
  height: 32px;
}

/* 脉冲动画 */
.pulse {
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  border: 2px solid rgba(255, 71, 87, 0.6);
  border-radius: 50%;
  animation: pulse-animation 1.5s infinite;
}

@keyframes pulse-animation {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.3);
    opacity: 0;
  }
}

/* 时长显示 */
.duration {
  margin-top: 1rem;
  font-size: 1.2rem;
  font-weight: bold;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  font-family: 'Courier New', monospace;
}

/* 状态文本 */
.status-text {
  color: white;
  font-size: 1rem;
  text-align: center;
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* 错误信息 */
.error-message {
  color: #ffeb3b;
  background: rgba(255, 235, 59, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.9rem;
  text-align: center;
  border: 1px solid rgba(255, 235, 59, 0.3);
}
</style>
